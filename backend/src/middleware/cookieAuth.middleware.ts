import { Request, Response, NextFunction } from 'express';
import jwt, { SignOptions } from 'jsonwebtoken';
import { JwtPayload } from '../types/express';
import { getEnv } from '../utils/envValidator';
import { Admin } from '../models/Admin';
import { User } from '../models/User';

/**
 * Authentication middleware using HTTP-only cookies
 * This is a more secure approach than using localStorage for tokens
 */
export const cookieAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from cookies instead of Authorization header
    const token = req.cookies.token;

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'No authentication token found'
      });
    }

    // Verify the token
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || 'your-secret-key'
    ) as JwtPayload;

    // Check if the token belongs to an admin
    if (decoded.role === 'admin' || decoded.role === 'super_admin' || decoded.isMainAdmin) {
      const admin = await Admin.findById(decoded.id);

      if (!admin) {
        return res.status(401).json({
          success: false,
          message: 'Authentication failed',
          error: 'Admin account not found'
        });
      }

      // Attach admin info to request
      req.user = {
        id: decoded.id,
        email: decoded.email || admin.email,
        role: decoded.role || admin.role,
        isMainAdmin: decoded.isMainAdmin || admin.isMainAdmin || false
      };
    } else {
      // Check if the token belongs to a regular user
      const user = await User.findById(decoded.id);

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication failed',
          error: 'User account not found'
        });
      }

      // Attach user info to request
      req.user = {
        id: decoded.id,
        email: decoded.email || user.email,
        role: decoded.role || user.role || 'user',
        isMainAdmin: false
      };
    }

    // Check if token is about to expire and refresh if needed
    const tokenExp = decoded.exp;
    const currentTime = Math.floor(Date.now() / 1000);

    // If token will expire in less than 15 minutes (900 seconds), refresh it
    if (tokenExp && tokenExp - currentTime < 900) {
      const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
      const jwtOptions: SignOptions = { expiresIn: '1d' }; // Fixed expiration time
      const newToken = jwt.sign(
        {
          id: req.user.id,
          email: req.user.email,
          role: req.user.role,
          isMainAdmin: req.user.isMainAdmin
        },
        jwtSecret,
        jwtOptions
      );

      // Set the new token in a cookie
      res.cookie('token', newToken, {
        httpOnly: true,
        secure: false, // Set to false in development to work with HTTP
        sameSite: 'lax', // Use 'lax' for better compatibility in development
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        path: '/'
      });
    }

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      // Clear the invalid token
      res.clearCookie('token');

      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: 'Invalid or expired token'
      });
    }

    console.error('Authentication error:', error);

    return res.status(500).json({
      success: false,
      message: 'Authentication error',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Admin authentication middleware
 * Requires the user to be an admin
 */
export const adminCookieAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await cookieAuth(req, res, () => {
      // Check if the user is an admin (either regular admin or main admin)
      if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super_admin' && !req.user.isMainAdmin)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          error: 'Admin privileges required'
        });
      }
      next();
    });
  } catch (error) {
    console.error('Admin authentication error:', error);
    res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: 'Please authenticate as an admin'
    });
  }
};

/**
 * Main admin authentication middleware
 * Requires the user to be a main admin
 */
export const mainAdminCookieAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await cookieAuth(req, res, () => {
      // Check if the user is a main admin
      if (!req.user || !req.user.isMainAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          error: 'Main admin privileges required'
        });
      }
      next();
    });
  } catch (error) {
    console.error('Main admin authentication error:', error);
    res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: 'Please authenticate as a main admin'
    });
  }
};

export default {
  cookieAuth,
  adminCookieAuth,
  mainAdminCookieAuth
};
