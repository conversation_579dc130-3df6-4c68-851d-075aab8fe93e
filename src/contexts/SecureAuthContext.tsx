import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import secureApiService from '../services/secureApiService';
import { clearAllLocalStorage } from '../utils/clearStorage';

// Define user and admin types
interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface Admin {
  id: number;
  name: string;
  email: string;
  role: string;
  isMainAdmin: boolean;
  privileges: string[];
}

// Define context state
interface AuthContextState {
  user: User | null;
  admin: Admin | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isMainAdmin: boolean;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  adminLogin: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
}

// Create context with default values
const SecureAuthContext = createContext<AuthContextState>({
  user: null,
  admin: null,
  isAuthenticated: false,
  isAdmin: false,
  isMainAdmin: false,
  loading: true,
  error: null,
  login: async () => {},
  adminLogin: async () => {},
  logout: async () => {},
  clearError: () => {},
});

// Create provider component
export const SecureAuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [isMainAdmin, setIsMainAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Clear error
  const clearError = () => setError(null);

  // Check if user is authenticated on mount
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        console.log('Checking authentication status...');
        setLoading(true);

        // Clear any old localStorage tokens to prevent conflicts
        console.log('Clearing old localStorage authentication data...');
        clearAllLocalStorage();

        // Try to get admin profile first - this will work if the HTTP-only cookie is present
        try {
          console.log('Attempting to get admin profile...');
          const response = await secureApiService.getAdminProfile();
          console.log('Admin profile response:', response);

          if (response.success && response.data && response.data.admin) {
            console.log('Setting admin state from profile');
            setAdmin(response.data.admin);
            setUser(null);
            setIsAuthenticated(true);
            setIsAdmin(true);
            setIsMainAdmin(response.data.admin.isMainAdmin);
            console.log('Admin authentication successful');
            setLoading(false);
            return; // Exit early if admin auth is successful
          } else {
            console.log('Admin profile response not successful or missing data');
          }
        } catch (adminError: any) {
          console.log('Admin profile error (this is normal if not logged in):', adminError.response?.status || adminError.message);
        }

        // If not an admin, try to get user profile
        try {
          console.log('Attempting to get user profile...');
          const response = await secureApiService.getProfile();
          console.log('User profile response:', response);

          if (response.success && response.data && response.data.user) {
            console.log('Setting user state from profile');
            setUser(response.data.user);
            setAdmin(null);
            setIsAuthenticated(true);
            setIsAdmin(false);
            setIsMainAdmin(false);
            console.log('User authentication successful');
            setLoading(false);
            return; // Exit early if user auth is successful
          } else {
            console.log('User profile response not successful or missing data');
          }
        } catch (userError: any) {
          console.log('User profile error (this is normal if not logged in):', userError.response?.status || userError.message);
        }

        // If we get here, we're not authenticated as either admin or user
        console.log('Not authenticated as either admin or user');
        setUser(null);
        setAdmin(null);
        setIsAuthenticated(false);
        setIsAdmin(false);
        setIsMainAdmin(false);
      } catch (error) {
        console.error('Error checking authentication status:', error);
        // Make sure to reset auth state on error
        setUser(null);
        setAdmin(null);
        setIsAuthenticated(false);
        setIsAdmin(false);
        setIsMainAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  // Login user
  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      clearError();

      const response = await secureApiService.login(email, password);

      if (response.success && response.data.user) {
        setUser(response.data.user);
        setAdmin(null);
        setIsAuthenticated(true);
        setIsAdmin(false);
        setIsMainAdmin(false);
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error: any) {
      setError(error.response?.data?.message || error.message || 'Login failed');
      setUser(null);
      setAdmin(null);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setIsMainAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  // Login admin
  const adminLogin = async (email: string, password: string) => {
    try {
      setLoading(true);
      clearError();

      console.log('Attempting admin login with credentials:', { email });
      const response = await secureApiService.adminLogin(email, password);
      console.log('Admin login response:', response);

      if (response.success && response.data.admin) {
        console.log('Admin login successful:', response.data.admin);

        // Set admin state
        setAdmin(response.data.admin);
        setUser(null);
        setIsAuthenticated(true);
        setIsAdmin(true);
        setIsMainAdmin(response.data.admin.isMainAdmin);

        console.log('Admin authentication state set successfully');
      } else {
        throw new Error(response.message || 'Admin login failed');
      }
    } catch (error: any) {
      console.error('Admin login error:', error);
      setError(error.response?.data?.message || error.message || 'Admin login failed');
      setUser(null);
      setAdmin(null);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setIsMainAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  // Logout
  const logout = async () => {
    try {
      setLoading(true);
      clearError();

      await secureApiService.logout();

      // Clear any localStorage data to ensure clean state
      clearAllLocalStorage();

      setUser(null);
      setAdmin(null);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setIsMainAdmin(false);
    } catch (error: any) {
      setError(error.response?.data?.message || error.message || 'Logout failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <SecureAuthContext.Provider
      value={{
        user,
        admin,
        isAuthenticated,
        isAdmin,
        isMainAdmin,
        loading,
        error,
        login,
        adminLogin,
        logout,
        clearError,
      }}
    >
      {children}
    </SecureAuthContext.Provider>
  );
};

// Create custom hook for using the auth context
export const useSecureAuth = () => useContext(SecureAuthContext);

export default SecureAuthContext;
